// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  
  // ข้อมูลส่วนตัว
  fullName  String
  gender    String   // 'male', 'female', 'other'
  birthDate DateTime
  
  // ข้อมูลร่างกาย
  height    Float    // ส่วนสูง (ซม.)
  weight    Float    // น้ำหนัก (กก.)
  activityLevel String // 'sedentary', 'light', 'moderate', 'active', 'very_active'
  
  // เป้าหมายและความชอบ
  goal      String   // 'weight_loss', 'maintain', 'weight_gain', 'muscle_gain', 'health'
  foodAllergies String? // อาหารที่แพ้
  dietaryPreferences String[] // ความชอบอาหาร ['vegetarian', 'vegan', 'keto', etc.]
  
  // ค่าคำนวณ
  bmr       Float?   // Basal Metabolic Rate
  tdee      Float?   // Total Daily Energy Expenditure
  
  // ข้อมูลระบบ
  role      String   @default("user") // 'user', 'nutritionist', 'admin'
  isActive  Boolean  @default(true)

  // Password reset fields
  resetToken       String?
  resetTokenExpiry DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  foodLogs     FoodLog[]
  ffqResponses FFQResponse[]
  reports      UserReport[]
}

// ฐานข้อมูลอาหาร
model Food {
  id          Int      @id @default(autoincrement())
  name        String
  category    String   // 'milk', 'meat', 'starch', 'vegetable', 'fruit', 'fat'
  
  // ค่าโภชนาการต่อหน่วยมาตรฐาน
  calories    Float    // แคลอรี่ต่อหน่วย
  protein     Float    // โปรตีน (กรัม)
  carbs       Float    // คาร์โบไฮเดรต (กรัม)  
  fat         Float    // ไขมัน (กรัม)
  fiber       Float    // ใยอาหาร (กรัม)
  
  // หน่วยมาตรฐาน
  standardUnit String  // 'tablespoon', 'teaspoon', 'cup', 'piece'
  
  // ข้อมูลเพิ่มเติม
  description String?
  imageUrl    String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  foodLogs    FoodLogItem[]
}

// การบันทึกอาหารรายวัน
model FoodLog {
  id        Int      @id @default(autoincrement())
  userId    Int
  date      DateTime // วันที่บันทึก
  mealType  String   // 'breakfast', 'lunch', 'dinner', 'snack'
  mealTime  DateTime // เวลาที่รับประทาน
  
  // ภาพถ่าย
  beforeImage String? // ภาพก่อนทาน
  afterImage  String? // ภาพหลังทาน
  
  // สถานะการตรวจสอบ
  isVerified Boolean @default(false)
  verifiedBy Int?    // ID ของนักโภชนาการที่ตรวจสอบ
  verifiedAt DateTime?
  notes      String? // หมายเหตุจากนักโภชนาการ
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  user      User           @relation(fields: [userId], references: [id])
  items     FoodLogItem[]
  
  @@index([userId, date])
}

// รายการอาหารในแต่ละมื้อ
model FoodLogItem {
  id         Int     @id @default(autoincrement())
  foodLogId  Int
  foodId     Int
  amount     Float   // ปริมาณ
  unit       String  // หน่วย ('tablespoon', 'teaspoon', 'cup', 'piece')
  
  // ค่าโภชนาการที่คำนวณ
  calories   Float
  protein    Float
  carbs      Float
  fat        Float
  
  createdAt  DateTime @default(now())
  
  // Relations
  foodLog    FoodLog @relation(fields: [foodLogId], references: [id], onDelete: Cascade)
  food       Food    @relation(fields: [foodId], references: [id])
}

// แบบสอบถามความถี่ในการรับประทานอาหาร (FFQ)
model FFQQuestion {
  id          Int      @id @default(autoincrement())
  question    String
  foodGroup   String   // หมวดอาหาร
  order       Int      // ลำดับคำถาม
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  
  // Relations
  responses   FFQResponse[]
}

model FFQResponse {
  id         Int      @id @default(autoincrement())
  userId     Int
  questionId Int
  frequency  String   // 'never', 'rarely', 'sometimes', 'often', 'daily'
  portion    String?  // ขนาดของส่วน 'small', 'medium', 'large'
  createdAt  DateTime @default(now())
  
  // Relations
  user       User        @relation(fields: [userId], references: [id])
  question   FFQQuestion @relation(fields: [questionId], references: [id])
  
  @@unique([userId, questionId])
}

// รายงานสำหรับผู้ใช้
model UserReport {
  id              Int      @id @default(autoincrement())
  userId          Int
  reportDate      DateTime
  
  // สรุปโภชนาการ
  totalCalories   Float
  avgProtein      Float
  avgCarbs        Float
  avgFat          Float
  avgFiber        Float
  
  // การประเมิน
  nutritionScore  Float?   // คะแนนโภชนาการ
  recommendations String[] // คำแนะนำ
  
  // ข้อมูลการสร้างรายงาน
  generatedBy     Int?     // ID ของนักโภชนาการ
  reportType      String   // 'weekly', 'monthly', 'custom'
  
  createdAt       DateTime @default(now())
  
  // Relations
  user            User     @relation(fields: [userId], references: [id])
}

// ระบบการแลกเปลี่ยนอาหาร
model FoodExchange {
  id           Int     @id @default(autoincrement())
  category     String  // หมวดอาหาร
  name         String  // ชื่ออาหาร
  amount       Float   // ปริมาณ
  unit         String  // หน่วย
  calories     Float   // แคลอรี่
  exchangeValue Float  // ค่าแลกเปลี่ยน (1 ส่วน = ? แคลอรี่)
  
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}