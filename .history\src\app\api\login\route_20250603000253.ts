
// src/app/api/login/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

// JWT Secret - ในการใช้งาน ควรเก็บใน environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface UserResponse {
  id: number;
  email: string;
  fullName: string;
  gender: string;
  height: number;
  weight: number;
  activityLevel: string;
  goal: string;
  foodAllergies?: string;
  bmr?: number;
  tdee?: number;
  role: string;
  bmi: number;
  age: number;
}

function calculateAge(birthDate: Date): number {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

function generateTokens(userId: number, email: string, rememberMe: boolean = false) {
  const payload = {
    userId,
    email,
    iat: Math.floor(Date.now() / 1000),
  };

  const accessToken = jwt.sign(payload, JWT_SECRET as jwt.Secret, {
    expiresIn: rememberMe ? '30d' : JWT_EXPIRES_IN,
  });

  const refreshToken = jwt.sign(
    { ...payload, type: 'refresh' },
    JWT_SECRET,
    { expiresIn: '30d' }
  );

  return { accessToken, refreshToken };
}

export async function POST(req: NextRequest) {
  try {
    console.log('🔐 Login API called');
    const body: LoginRequest = await req.json();
    console.log('📝 Login attempt for:', body.email);

    const { email, password, rememberMe = false } = body;

    // Input validation
    if (!email || !password) {
      return NextResponse.json({
        error: 'กรอก ชื่อผู้ใช้ และ รหัสผ่าน'
      }, { status: 400 });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        error: 'อีเมลไม่ถูกต้อง'
      }, { status: 400 });
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: {
        email: email.toLowerCase()
      }
    });

    if (!user) {
      console.log('❌ User not found:', email);
      return NextResponse.json({
        error: 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
      }, { status: 401 });
    }

    // Check if user is active
    if (!user.isActive) {
      console.log('❌ User account is inactive:', email);
      return NextResponse.json({
        error: 'บัญชีผู้ใช้ถูกระงับ กรุณาติดต่อผู้ดูแลระบบ'
      }, { status: 403 });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log('❌ Invalid password for user:', email);
      return NextResponse.json({
        error: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'
      }, { status: 401 });
    }

    // Generate JWT tokens
    const { accessToken, refreshToken } = generateTokens(user.id, user.email, rememberMe);

    // Calculate age and BMI
    const age = calculateAge(user.birthDate);
    const bmi = Math.round((user.weight / Math.pow(user.height / 100, 2)) * 10) / 10;

    // Prepare user response (exclude sensitive data)
    const userResponse: UserResponse = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      gender: user.gender,
      height: user.height,
      weight: user.weight,
      activityLevel: user.activityLevel,
      goal: user.goal,
      foodAllergies: user.foodAllergies || undefined,
      bmr: user.bmr || undefined,
      tdee: user.tdee || undefined,
      role: user.role,
      bmi,
      age
    };

    console.log('✅ Login successful for user:', email);

    // Set HTTP-only cookie for refresh token (more secure)
    const response = NextResponse.json({
      message: 'เข้าสู่ระบบสำเร็จ! ยินดีต้อนรับกลับ 🎉',
      user: userResponse,
      accessToken,
      expiresIn: rememberMe ? '30d' : JWT_EXPIRES_IN
    }, { status: 200 });

    // Set refresh token as HTTP-only cookie
    response.cookies.set('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      path: '/'
    });

    return response;

  } catch (error: any) {
    console.error('❌ Login error:', error.message);

    // Handle specific Prisma errors
    if (error.code === 'P2025') {
      return NextResponse.json({
        error: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'
      }, { status: 401 });
    }

    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });

  } finally {
    await prisma.$disconnect();
  }
}

// Logout endpoint
export async function DELETE(req: NextRequest) {
  try {
    console.log('🚪 Logout API called');

    const response = NextResponse.json({
      message: 'ออกจากระบบสำเร็จ'
    }, { status: 200 });

    // Clear refresh token cookie
    response.cookies.set('refreshToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    });

    return response;

  } catch (error: any) {
    console.error('❌ Logout error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการออกจากระบบ'
    }, { status: 500 });
  }
}
