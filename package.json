{"name": "project-nutrition", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "postinstall": "prisma generate", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.8.2", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "bcrypt": "^6.0.0", "dotenv": "^16.5.0", "framer-motion": "^12.15.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.8.2", "tailwindcss": "^4", "typescript": "^5"}}