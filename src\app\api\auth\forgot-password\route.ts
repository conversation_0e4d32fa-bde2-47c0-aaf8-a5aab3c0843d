// src/app/api/auth/forgot-password/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

// สำหรับการส่งอีเมล (ในอนาคตจะใช้ service จริง)
async function sendPasswordResetEmail(email: string, resetToken: string) {
  // TODO: Implement actual email sending
  console.log(`📧 Password reset email would be sent to: ${email}`);
  console.log(`🔗 Reset link: ${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`);
  
  // จำลองการส่งอีเมล
  return Promise.resolve(true);
}

export async function POST(req: NextRequest) {
  try {
    console.log('🔑 Forgot password API called');
    const body = await req.json();
    const { email } = body;

    // Input validation
    if (!email) {
      return NextResponse.json({
        error: 'กรุณากรอกอีเมล'
      }, { status: 400 });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        error: 'รูปแบบอีเมลไม่ถูกต้อง'
      }, { status: 400 });
    }

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    // Security: Always return success even if user doesn't exist
    // เพื่อป้องกันการ enumerate users
    if (!user) {
      console.log('❌ User not found for forgot password:', email);
      return NextResponse.json({
        message: 'หากอีเมลนี้มีอยู่ในระบบ เราจะส่งลิงก์รีเซ็ตรหัสผ่านไปให้'
      }, { status: 200 });
    }

    // Check if user is active
    if (!user.isActive) {
      console.log('❌ Inactive user tried forgot password:', email);
      return NextResponse.json({
        message: 'หากอีเมลนี้มีอยู่ในระบบ เราจะส่งลิงก์รีเซ็ตรหัสผ่านไปให้'
      }, { status: 200 });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

    // Save reset token to database
    await prisma.user.update({
      where: { id: user.id },
      data: {
        // Note: You'll need to add these fields to your User model
        resetToken,
        resetTokenExpiry,
        updatedAt: new Date()
      }
    });

    // Send password reset email
    try {
      await sendPasswordResetEmail(user.email, resetToken);
      console.log('✅ Password reset email sent to:', user.email);
    } catch (emailError) {
      console.error('❌ Failed to send reset email:', emailError);
      // Don't expose email sending errors to user
    }

    return NextResponse.json({
      message: 'หากอีเมลนี้มีอยู่ในระบบ เราจะส่งลิงก์รีเซ็ตรหัสผ่านไปให้'
    }, { status: 200 });

  } catch (error: any) {
    console.error('❌ Forgot password error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });

  } finally {
    await prisma.$disconnect();
  }
}
