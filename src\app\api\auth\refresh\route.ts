// src/app/api/auth/refresh/route.ts
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { createRateLimiter, createRateLimitResponse, addRateLimitHeaders } from '@/lib/rate-limit';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

interface RefreshTokenPayload {
  userId: number;
  email: string;
  type: string;
  iat: number;
  exp: number;
}

export async function POST(req: NextRequest) {
  try {
    console.log('🔄 Refresh token API called');

    // Rate limiting check
    const refreshLimiter = createRateLimiter('refreshToken');
    const rateLimitResult = refreshLimiter(req);
    
    if (!rateLimitResult.success) {
      console.log('🚫 Rate limit exceeded for refresh token');
      return createRateLimitResponse(rateLimitResult);
    }

    // Get refresh token from cookie
    const refreshToken = req.cookies.get('refreshToken')?.value;

    if (!refreshToken) {
      return NextResponse.json({
        error: 'ไม่พบ refresh token กรุณาเข้าสู่ระบบใหม่'
      }, { status: 401 });
    }

    // Verify refresh token
    let decoded: RefreshTokenPayload;
    try {
      decoded = jwt.verify(refreshToken, JWT_SECRET) as RefreshTokenPayload;
    } catch (error) {
      console.log('❌ Invalid refresh token');
      return NextResponse.json({
        error: 'Refresh token ไม่ถูกต้อง กรุณาเข้าสู่ระบบใหม่'
      }, { status: 401 });
    }

    // Check if it's actually a refresh token
    if (decoded.type !== 'refresh') {
      return NextResponse.json({
        error: 'Token ไม่ถูกต้อง'
      }, { status: 401 });
    }

    // Generate new access token
    const newAccessToken = jwt.sign(
      {
        userId: decoded.userId,
        email: decoded.email,
        iat: Math.floor(Date.now() / 1000),
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    console.log('✅ New access token generated for user:', decoded.email);

    const response = NextResponse.json({
      message: 'สร้าง access token ใหม่สำเร็จ',
      accessToken: newAccessToken,
      expiresIn: JWT_EXPIRES_IN
    }, { status: 200 });

    // Add rate limit headers to successful response
    addRateLimitHeaders(response, rateLimitResult);

    return response;

  } catch (error: any) {
    console.error('❌ Refresh token error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการสร้าง token ใหม่',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
