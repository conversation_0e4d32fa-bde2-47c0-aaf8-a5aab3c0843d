// src/app/api/auth/reset-password/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

export async function POST(req: NextRequest) {
  try {
    console.log('🔄 Reset password API called');
    const body = await req.json();
    const { token, newPassword, confirmPassword } = body;

    // Input validation
    if (!token || !newPassword || !confirmPassword) {
      return NextResponse.json({
        error: 'กรุณากรอกข้อมูลให้ครบถ้วน'
      }, { status: 400 });
    }

    // Password validation
    if (newPassword.length < 8) {
      return NextResponse.json({
        error: 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร'
      }, { status: 400 });
    }

    if (newPassword !== confirmPassword) {
      return NextResponse.json({
        error: 'รหัสผ่านและการยืนยันรหัสผ่านไม่ตรงกัน'
      }, { status: 400 });
    }

    // Find user by reset token
    const user = await prisma.user.findFirst({
      where: {
        resetToken: token,
        resetTokenExpiry: {
          gt: new Date() // Token must not be expired
        },
        isActive: true
      }
    });

    if (!user) {
      return NextResponse.json({
        error: 'ลิงก์รีเซ็ตรหัสผ่านไม่ถูกต้องหรือหมดอายุแล้ว'
      }, { status: 400 });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update user password and clear reset token
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetToken: null,
        resetTokenExpiry: null,
        updatedAt: new Date()
      }
    });

    console.log('✅ Password reset successful for user:', user.email);

    return NextResponse.json({
      message: 'รีเซ็ตรหัสผ่านสำเร็จ กรุณาเข้าสู่ระบบด้วยรหัสผ่านใหม่'
    }, { status: 200 });

  } catch (error: any) {
    console.error('❌ Reset password error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน กรุณาลองใหม่อีกครั้ง',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });

  } finally {
    await prisma.$disconnect();
  }
}
