// src/app/api/clear-session/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getIronSession } from 'iron-session';
import { sessionOptions, type SessionData } from '@/lib/session';

// DELETE /api/clear-session - เคลียร์ session data ทั้งหมด (รวม autofill)
export async function DELETE(req: NextRequest) {
  try {
    console.log('🧹 Clear session API called');

    const response = NextResponse.json({
      message: 'เคลียร์ session data สำเร็จ'
    }, { status: 200 });

    // Clear all session data
    const session = await getIronSession<SessionData>(req, response, sessionOptions);
    
    session.user = undefined;
    session.isLoggedIn = false;
    session.loginHistory = [];
    session.savedCredentials = [];
    
    await session.save();

    console.log('✅ All session data cleared');
    return response;

  } catch (error: any) {
    console.error('❌ Clear session error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการเคลียร์ session',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
