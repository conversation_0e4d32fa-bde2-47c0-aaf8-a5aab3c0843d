// src/components/AuthProvider.tsx
"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { initializeAuth, autoRefreshToken, clearStoredUser, type StoredUser } from '@/lib/auth-utils';

interface AuthContextType {
  user: StoredUser | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  login: (user: StoredUser, token: string, rememberMe?: boolean) => void;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<StoredUser | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth on mount
  useEffect(() => {
    const initAuth = async () => {
      try {
        const authenticatedUser = await initializeAuth();
        
        if (authenticatedUser) {
          setUser(authenticatedUser);
          setIsLoggedIn(true);
          console.log('✅ Auth initialized:', authenticatedUser.fullName);
        } else {
          setUser(null);
          setIsLoggedIn(false);
          console.log('❌ No authenticated user found');
        }
      } catch (error) {
        console.error('❌ Auth initialization failed:', error);
        setUser(null);
        setIsLoggedIn(false);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // Auto-refresh token every 30 minutes if remember me is enabled
  useEffect(() => {
    if (!isLoggedIn) return;

    const interval = setInterval(async () => {
      // Only auto-refresh if remember me is enabled (localStorage only)
      const rememberMe = localStorage.getItem('rememberMe') === 'true';

      if (rememberMe) {
        console.log('🔄 Attempting auto token refresh...');
        const refreshed = await autoRefreshToken();

        if (!refreshed) {
          console.log('❌ Auto refresh failed, logging out');
          logout();
        } else {
          console.log('✅ Token auto-refreshed successfully');
        }
      } else {
        console.log('⏭️ Auto refresh skipped: Session-only login');
      }
    }, 30 * 60 * 1000); // 30 minutes

    return () => clearInterval(interval);
  }, [isLoggedIn]);

  const login = (userData: StoredUser, token: string, rememberMe: boolean = false) => {
    setUser(userData);
    setIsLoggedIn(true);

    if (rememberMe) {
      // Store in localStorage for persistent login
      localStorage.setItem('user', JSON.stringify(userData));
      localStorage.setItem('accessToken', token);
      localStorage.setItem('rememberMe', 'true');
      console.log('✅ User logged in with remember me:', userData.fullName);
    } else {
      // Store in sessionStorage for session-only login
      sessionStorage.setItem('user', JSON.stringify(userData));
      sessionStorage.setItem('accessToken', token);
      sessionStorage.setItem('rememberMe', 'false');

      // Clear any existing localStorage data
      localStorage.removeItem('user');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('rememberMe');

      console.log('✅ User logged in (session only):', userData.fullName);
    }
  };

  const logout = async () => {
    try {
      // Call logout API
      await fetch('/api/login', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });
    } catch (error) {
      console.error('❌ Logout API error:', error);
    } finally {
      // Clear state and storage
      setUser(null);
      setIsLoggedIn(false);
      clearStoredUser();
      
      console.log('✅ User logged out');
    }
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.accessToken) {
          localStorage.setItem('accessToken', data.accessToken);
          console.log('✅ Token refreshed manually');
          return true;
        }
      }
    } catch (error) {
      console.error('❌ Manual token refresh failed:', error);
    }
    
    return false;
  };

  const value: AuthContextType = {
    user,
    isLoggedIn,
    isLoading,
    login,
    logout,
    refreshToken
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
