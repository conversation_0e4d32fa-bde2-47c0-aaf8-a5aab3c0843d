// src/lib/auth-utils.ts
import { NextRequest } from 'next/server';

export interface StoredUser {
  id: number;
  email: string;
  fullName: string;
  gender: string;
  height: number;
  weight: number;
  activityLevel: string;
  goal: string;
  foodAllergies?: string;
  bmr?: number;
  tdee?: number;
  role: string;
  bmi: number;
  age: number;
}

/**
 * Check if user has remember me enabled from cookies
 */
export function hasRememberMe(req?: NextRequest): boolean {
  if (typeof window !== 'undefined') {
    // Client-side check
    return document.cookie.includes('rememberMe=true');
  } else if (req) {
    // Server-side check
    return req.cookies.get('rememberMe')?.value === 'true';
  }
  return false;
}

/**
 * Get stored user data from localStorage or sessionStorage
 */
export function getStoredUser(): StoredUser | null {
  if (typeof window === 'undefined') return null;

  try {
    // Check localStorage first (for remember me)
    let userStr = localStorage.getItem('user');
    let token = localStorage.getItem('accessToken');

    if (userStr && token) {
      return JSON.parse(userStr);
    }

    // Check sessionStorage (for session only)
    userStr = sessionStorage.getItem('user');
    token = sessionStorage.getItem('accessToken');

    if (userStr && token) {
      return JSON.parse(userStr);
    }

    return null;
  } catch (error) {
    console.error('Error getting stored user:', error);
    return null;
  }
}

/**
 * Store user data and token
 */
export function storeUserData(user: StoredUser, token: string, rememberMe: boolean = false): void {
  if (typeof window === 'undefined') return;

  try {
    if (rememberMe) {
      // Store in localStorage for persistent login
      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('accessToken', token);
      localStorage.setItem('rememberMe', 'true');
      console.log('✅ User data stored in localStorage (persistent)');
    } else {
      // Store in sessionStorage for session-only login
      sessionStorage.setItem('user', JSON.stringify(user));
      sessionStorage.setItem('accessToken', token);
      sessionStorage.setItem('rememberMe', 'false');

      // Clear any existing localStorage data
      localStorage.removeItem('user');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('rememberMe');

      console.log('✅ User data stored in sessionStorage (session only)');
    }
  } catch (error) {
    console.error('Error storing user data:', error);
  }
}

/**
 * Clear all stored user data
 */
export function clearStoredUser(): void {
  if (typeof window === 'undefined') return;

  try {
    // Clear localStorage
    localStorage.removeItem('user');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('rememberMe');

    // Clear sessionStorage
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('accessToken');
    sessionStorage.removeItem('rememberMe');

    console.log('✅ User data cleared from both localStorage and sessionStorage');
  } catch (error) {
    console.error('Error clearing user data:', error);
  }
}

/**
 * Check if user is logged in
 */
export function isLoggedIn(): boolean {
  if (typeof window === 'undefined') return false;

  // Check localStorage first
  let token = localStorage.getItem('accessToken');
  let user = localStorage.getItem('user');

  if (token && user) return true;

  // Check sessionStorage
  token = sessionStorage.getItem('accessToken');
  user = sessionStorage.getItem('user');

  return !!(token && user);
}

/**
 * Auto-refresh token if remember me is enabled
 */
export async function autoRefreshToken(): Promise<boolean> {
  if (typeof window === 'undefined') return false;

  // Check if remember me is enabled (only in localStorage)
  const rememberMe = localStorage.getItem('rememberMe') === 'true';

  if (!rememberMe) {
    console.log('❌ Auto-refresh skipped: Remember me not enabled');
    return false;
  }
  
  try {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      if (data.accessToken) {
        localStorage.setItem('accessToken', data.accessToken);
        console.log('✅ Token auto-refreshed successfully');
        return true;
      }
    }
  } catch (error) {
    console.error('❌ Auto-refresh failed:', error);
  }
  
  return false;
}

/**
 * Get authorization header for API calls
 */
export function getAuthHeader(): Record<string, string> {
  if (typeof window === 'undefined') return {};

  // Check localStorage first
  let token = localStorage.getItem('accessToken');

  if (!token) {
    // Check sessionStorage
    token = sessionStorage.getItem('accessToken');
  }

  if (!token) return {};

  return {
    'Authorization': `Bearer ${token}`
  };
}

/**
 * Check if token is expired (basic check)
 */
export function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiry:', error);
    return true;
  }
}

/**
 * Initialize auth state on app load - Updated for session-based auth
 */
export async function initializeAuth(): Promise<StoredUser | null> {
  if (typeof window === 'undefined') return null;

  try {
    // Check session-based authentication first
    const sessionResponse = await fetch('/api/session', {
      method: 'GET',
      credentials: 'include'
    });

    if (sessionResponse.ok) {
      const data = await sessionResponse.json();

      if (data.currentSession && data.currentSession.user) {
        const user = data.currentSession.user;

        // Convert session user to StoredUser format
        const storedUser: StoredUser = {
          id: user.id,
          email: user.email,
          fullName: user.fullName,
          gender: user.gender,
          height: user.height,
          weight: user.weight,
          activityLevel: user.activityLevel,
          goal: user.goal,
          foodAllergies: user.foodAllergies,
          bmr: user.bmr,
          tdee: user.tdee,
          role: user.role,
          bmi: user.bmi,
          age: user.age
        };

        console.log('✅ Session-based auth initialized:', user.fullName);
        return storedUser;
      }
    }

    // Fallback to JWT-based auth for backward compatibility
    let token = localStorage.getItem('accessToken');
    let rememberMe = localStorage.getItem('rememberMe') === 'true';

    if (token && rememberMe) {
      // Check if token is expired
      if (isTokenExpired(token)) {
        // Try to refresh token
        const refreshed = await autoRefreshToken();
        if (refreshed) {
          return getStoredUser();
        } else {
          // Clear expired data
          clearStoredUser();
          return null;
        }
      }
      return getStoredUser();
    }

    // Check sessionStorage (for session only)
    token = sessionStorage.getItem('accessToken');

    if (token) {
      // Check if token is expired
      if (isTokenExpired(token)) {
        // Clear expired session data (no auto-refresh for session)
        sessionStorage.removeItem('user');
        sessionStorage.removeItem('accessToken');
        sessionStorage.removeItem('rememberMe');
        return null;
      }
      return getStoredUser();
    }

    return null;
  } catch (error) {
    console.error('❌ Auth initialization error:', error);
    return null;
  }
}
