// src/middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createRateLimiter, createRateLimitResponse } from '@/lib/rate-limit';

export function middleware(request: NextRequest) {
  // Handle preflight requests first
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }

  // Skip rate limiting for specific endpoints that have their own rate limiting
  const skipRateLimit = [
    '/api/login',
    '/api/register',
    '/api/auth/refresh',
    '/api/auth/forgot-password',
    '/api/session',
    '/api/autofill'
  ];

  const shouldSkipRateLimit = skipRateLimit.some(path =>
    request.nextUrl.pathname.startsWith(path)
  );

  if (!shouldSkipRateLimit) {
    // Apply general rate limiting
    const generalLimiter = createRateLimiter('general');
    const rateLimitResult = generalLimiter(request);

    if (!rateLimitResult.success) {
      console.log('🚫 General rate limit exceeded for:', request.nextUrl.pathname);
      return createRateLimitResponse(rateLimitResult);
    }
  }

  // Handle CORS
  const response = NextResponse.next();
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  return response;
}

export const config = {
  matcher: '/api/:path*',
};